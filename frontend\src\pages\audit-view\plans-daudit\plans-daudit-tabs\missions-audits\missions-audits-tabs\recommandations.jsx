import React, { useState, useEffect } from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Plus, Search, Edit, Trash2, ExternalLink, Loader2 } from "lucide-react";
import { toast } from "sonner";
import { useNavigate, useParams } from "react-router-dom";
import { useOutletContext } from "react-router-dom";
import axios from 'axios';
import { getApiBaseUrl } from '@/utils/api-config';

// Import recommandation icon
import recommandationIcon from '@/assets/recommandation.png';

function RecommandationsTab() {
  const navigate = useNavigate();
  const { planId, missionAuditId } = useParams();
  const { missionAudit } = useOutletContext() || {};

  const [recommendations, setRecommendations] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");

  // Fetch recommendations for the current mission
  useEffect(() => {
    const fetchRecommendations = async () => {
      if (!missionAuditId) return;

      try {
        setIsLoading(true);
        // Fetch recommendations by mission ID
        const response = await axios.get(`${getApiBaseUrl()}/audit-recommendations/mission/${missionAuditId}`, {
          withCredentials: true,
          headers: { 'Content-Type': 'application/json' }
        });

        if (response.data.success) {
          setRecommendations(response.data.data || []);
        } else {
          console.error('Failed to fetch recommendations:', response.data.message);
          setRecommendations([]);
        }
      } catch (error) {
        console.error('Error fetching recommendations:', error);
        toast.error('Erreur lors du chargement des recommandations');
        setRecommendations([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchRecommendations();
  }, [missionAuditId]);

  // Filter recommendations based on search term
  const filteredRecommendations = recommendations.filter(recommendation =>
    recommendation.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    recommendation.nom?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Navigate to recommendation detail page
  const navigateToRecommendation = (recommendationId) => {
    // Find the constat that contains this recommendation
    // For now, we'll navigate to a general recommendation page
    if (planId && missionAuditId) {
      navigate(`/audit/plans-daudit/${planId}/missions-audits/${missionAuditId}/recommandations/${recommendationId}`);
    }
  };

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return "Non définie";
    try {
      return new Date(dateString).toLocaleDateString("fr-FR", {
        year: "numeric",
        month: "short",
        day: "numeric",
      });
    } catch {
      return dateString;
    }
  };

  // Get priority badge for recommendation
  const getPriorityBadge = (priority) => {
    switch (priority) {
      case 'très fort':
      case 'High':
        return <Badge className="bg-red-100 text-red-800">Très fort</Badge>;
      case 'fort':
      case 'Medium-High':
        return <Badge className="bg-orange-100 text-orange-800">Fort</Badge>;
      case 'moyen':
      case 'Medium':
        return <Badge className="bg-yellow-100 text-yellow-800">Moyen</Badge>;
      case 'faible':
      case 'Low':
        return <Badge className="bg-green-100 text-green-800">Faible</Badge>;
      case 'très faible':
      case 'Very Low':
        return <Badge className="bg-blue-100 text-blue-800">Très faible</Badge>;
      default:
        return <Badge variant="outline">{priority || 'Non défini'}</Badge>;
    }
  };

  // Get status badge for recommendation
  const getStatusBadge = (status) => {
    switch (status) {
      case 'Terminé':
      case 'Completed':
        return <Badge className="bg-green-100 text-green-800">Terminé</Badge>;
      case 'En cours':
      case 'In Progress':
        return <Badge className="bg-blue-100 text-blue-800">En cours</Badge>;
      case 'Planifié':
      case 'Planned':
        return <Badge className="bg-yellow-100 text-yellow-800">Planifié</Badge>;
      case 'Non démarré':
      case 'Not Started':
        return <Badge className="bg-gray-100 text-gray-800">Non démarré</Badge>;
      default:
        return <Badge variant="outline">{status || 'Non défini'}</Badge>;
    }
  };

  return (
    <div className="space-y-6 py-4">
      {/* Header with search and add button */}
      <div className="mb-4 flex justify-between items-center">
        <div className="relative w-64">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Rechercher dans les recommandations..."
            className="pl-8"
            value={searchTerm}
            onChange={e => setSearchTerm(e.target.value)}
          />
        </div>
        <Button
          className="bg-[#F62D51] hover:bg-[#F62D51]/90"
          onClick={() => {
            // Navigate to create new recommendation page
            if (planId && missionAuditId) {
              navigate(`/audit/plans-daudit/${planId}/missions-audits/${missionAuditId}/recommandations/new`);
            }
          }}
        >
          <Plus className="h-4 w-4 mr-2" />
          Ajouter une Recommandation
        </Button>
      </div>

      {/* Recommendations Table */}
      <Card className="overflow-hidden">
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <Table className="min-w-full">
              <TableHeader>
                <TableRow className="bg-gray-50 hover:bg-gray-100/50">
                  <TableHead className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nom</TableHead>
                  <TableHead className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Priorité</TableHead>
                  <TableHead className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Statut</TableHead>
                  <TableHead className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Responsable</TableHead>
                  <TableHead className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Avancement</TableHead>
                  <TableHead className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Échéance</TableHead>
                  <TableHead className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody className="divide-y divide-gray-200">
                {isLoading ? (
                  <TableRow>
                    <TableCell colSpan={7} className="px-4 py-10 text-center">
                      <div className="flex items-center justify-center">
                        <Loader2 className="h-8 w-8 animate-spin text-[#F62D51]" />
                      </div>
                    </TableCell>
                  </TableRow>
                ) : filteredRecommendations.length > 0 ? (
                  filteredRecommendations.map(recommendation => (
                    <TableRow
                      key={recommendation.id}
                      className="hover:bg-gray-50/50 cursor-pointer"
                      onClick={() => navigateToRecommendation(recommendation.id)}
                    >
                      <TableCell className="px-4 py-3 text-sm font-medium text-gray-900 whitespace-nowrap">
                        <div className="flex items-center">
                          <img src={recommandationIcon} alt="Recommandation" className="h-4 w-4 mr-2 flex-shrink-0" />
                          <span>{recommendation.name || recommendation.nom}</span>
                          <ExternalLink className="h-3.5 w-3.5 ml-2 text-gray-400" />
                        </div>
                      </TableCell>
                      <TableCell className="px-4 py-3 text-sm whitespace-nowrap">
                        {getPriorityBadge(recommendation.priorite || recommendation.priority)}
                      </TableCell>
                      <TableCell className="px-4 py-3 text-sm whitespace-nowrap">
                        {getStatusBadge(recommendation.status || recommendation.statut)}
                      </TableCell>
                      <TableCell className="px-4 py-3 text-sm text-gray-500 whitespace-nowrap">
                        {recommendation.responsableUser?.username || recommendation.responsable || 'Non assigné'}
                      </TableCell>
                      <TableCell className="px-4 py-3 text-sm whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="w-full bg-gray-200 rounded-full h-2.5 mr-2">
                            <div className="h-2.5 rounded-full bg-blue-500" style={{ width: `${recommendation.avancement || 0}%` }}></div>
                          </div>
                          <span>{recommendation.avancement || 0}%</span>
                        </div>
                      </TableCell>
                      <TableCell className="px-4 py-3 text-sm text-gray-500 whitespace-nowrap">
                        {formatDate(recommendation.echeance || recommendation.deadline)}
                      </TableCell>
                      <TableCell className="px-4 py-3 text-sm whitespace-nowrap">
                        <div className="flex justify-end gap-2">
                          <Button
                            size="sm"
                            variant="ghost"
                            className="h-8 w-8 p-0"
                            onClick={e => {
                              e.stopPropagation();
                              navigateToRecommendation(recommendation.id);
                            }}
                          >
                            <Edit className="h-4 w-4 text-blue-600" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={7} className="px-4 py-10 text-center text-sm text-gray-500">
                      {searchTerm ? 'Aucune recommandation trouvée pour cette recherche' : 'Aucune recommandation dans cette mission'}
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );

}

export default RecommandationsTab;