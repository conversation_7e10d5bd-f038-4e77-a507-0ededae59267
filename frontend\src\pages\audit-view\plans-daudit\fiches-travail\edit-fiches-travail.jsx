import { useState, useEffect, useCallback, createContext, useContext } from "react";
import { useParams, useNavigate, useLocation } from "react-router-dom";
import {
  FileText,
  Loader2,
  Activity,
  ClipboardList,
  ArrowLeft
} from "lucide-react";
import { Button } from "@/components/ui/button";
import DetailHeader from "@/components/ui/detail-header";
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from "@/components/ui/breadcrumb";
import { toast } from "sonner";
import { getApiBaseUrl } from "@/utils/api-config";
import { getFicheDeTravailById } from "@/services/fiche-de-travail-service";
import axios from 'axios';
import CaracteristiquesTab from "./fiches-travail-tabs/caracteristiques/caracteristiques.jsx";
import FichesTestTab from "./fiches-travail-tabs/fiches-test/fiches-test.jsx";

// Import fiche-travail icon
import ficheTravailIcon from '@/assets/fiche-travail.png';

export const OutletContext = createContext(null);
export const useCustomOutletContext = () => {
  const context = useContext(OutletContext);
  if (context === undefined) {
    throw new Error('useCustomOutletContext must be used within an OutletContextProvider');
  }
  return context;
};

function EditFichesTravail() {
  const { planId, missionAuditId, ficheId } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const API_BASE_URL = getApiBaseUrl();

  const [fiche, setFiche] = useState(null);
  const [auditPlan, setAuditPlan] = useState(null);
  const [missionAudit, setMissionAudit] = useState(null);
  const [activity, setActivity] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      setError(null);
      try {
        // Fetch fiche de travail
        const response = await getFicheDeTravailById(ficheId);
        if (response && response.success) {
          setFiche(response.data);

          // Fetch audit plan data if planId is available
          if (planId) {
            try {
              const planResponse = await axios.get(`${getApiBaseUrl()}/audit-plans/${planId}`, {
                withCredentials: true,
                headers: {
                  'Content-Type': 'application/json'
                }
              });
              if (planResponse.data.success) {
                setAuditPlan(planResponse.data.data);
              }
            } catch (planError) {
              console.error("Error fetching audit plan:", planError);
            }
          }

          // Fetch mission audit data if missionAuditId is available
          if (missionAuditId) {
            try {
              const missionResponse = await axios.get(`${getApiBaseUrl()}/audit-missions/${missionAuditId}`, {
                withCredentials: true,
                headers: {
                  'Content-Type': 'application/json'
                }
              });
              if (missionResponse.data.success) {
                setMissionAudit(missionResponse.data.data);
              }
            } catch (missionError) {
              console.error("Error fetching mission audit:", missionError);
            }
          }

          // Fetch activity data if auditActivityID is available
          if (response.data.auditActivityID) {
            try {
              const activityResponse = await axios.get(`${getApiBaseUrl()}/audit-activities/${response.data.auditActivityID}`, {
                withCredentials: true,
                headers: {
                  'Content-Type': 'application/json'
                }
              });
              if (activityResponse.data.success) {
                setActivity(activityResponse.data.data);
              }
            } catch (activityError) {
              console.error("Error fetching activity:", activityError);
            }
          }
        } else {
          throw new Error(response?.message || "Fiche de travail non trouvée");
        }
      } catch (error) {
        console.error('Error fetching fiche:', error);
        setError(error.message || "Une erreur est survenue lors du chargement de la fiche de travail");
        toast.error("Erreur lors du chargement de la fiche de travail");
      } finally {
        setIsLoading(false);
      }
    };

    if (ficheId) {
      fetchData();
    }
  }, [ficheId, planId, missionAuditId]);

  // Get the current tab from the URL
  const getCurrentTab = useCallback(() => {
    const path = location.pathname;
    if (path.includes('/fiches-test')) return 'fiches-test';
    return 'caracteristiques'; // Default tab
  }, [location.pathname]);

  // Define tabs
  const tabs = [
    { id: "caracteristiques", label: "Caractéristiques", icon: <FileText className="h-4 w-4" /> },
    { id: "fiches-test", label: "Fiches de test", icon: <ClipboardList className="h-4 w-4" /> },
  ];

  // Navigate to tab
  const navigateToTab = (tabId) => {
    let baseUrl = `/audit/plans-daudit/${planId}/missions-audits/${missionAuditId}/fiches-travail/${ficheId}`;
    switch (tabId) {
      case "caracteristiques":
        navigate(baseUrl);
        break;
      case "fiches-test":
        navigate(`${baseUrl}/fiches-test`);
        break;
      default:
        navigate(baseUrl);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#F62D51]"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4">
        <Button variant="outline" onClick={() => navigate(-1)} className="mb-4">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Retour
        </Button>
        <div className="text-center py-8">
          <h2 className="text-2xl font-semibold text-gray-800 mb-2">Erreur</h2>
          <p className="text-gray-600">{error}</p>
        </div>
      </div>
    );
  }

  if (!fiche) {
    return (
      <div className="p-4">
        <Button variant="outline" onClick={() => navigate(-1)} className="mb-4">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Retour
        </Button>
        <div className="text-center py-8">
          <h2 className="text-2xl font-semibold text-gray-800 mb-2">Fiche de travail non trouvée</h2>
          <p className="text-gray-600">La fiche de travail demandée n'existe pas ou a été supprimée.</p>
        </div>
      </div>
    );
  }

  // Generate metadata
  const metadata = [
    `Mission: ${fiche?.auditMission?.name || 'N/A'}`,
    `Activité: ${fiche?.auditActivity?.name || 'N/A'}`
  ];

  // Get status badge details (mocked for now)
  const getStatusBadgeInfo = () => {
    return { label: 'En cours', variant: 'default', color: 'bg-blue-100 text-blue-800' };
  };

  // Breadcrumb with real data and clickable navigation
  const breadcrumb = (
    <Breadcrumb>
      <BreadcrumbList className="text-sm">
        <BreadcrumbItem>
          <BreadcrumbLink
            href="/audit/plans-daudit"
            className="text-gray-500 font-medium hover:text-blue-900 transition-colors"
            onClick={(e) => {
              e.preventDefault();
              navigate('/audit/plans-daudit');
            }}
          >
            Audit
          </BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbSeparator />
        {planId && (
          <>
            <BreadcrumbItem>
              <BreadcrumbLink
                href={`/audit/plans-daudit/${planId}`}
                className="text-gray-500 font-medium hover:text-blue-900 transition-colors"
                onClick={(e) => {
                  e.preventDefault();
                  navigate(`/audit/plans-daudit/${planId}`);
                }}
              >
                {auditPlan?.name || `Plan ${planId}`}
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
          </>
        )}
        <BreadcrumbItem>
          <BreadcrumbLink
            href={planId
              ? `/audit/plans-daudit/${planId}/missions-audits/${missionAuditId}`
              : `/audit/missions-audits/${missionAuditId}`
            }
            className="text-gray-500 font-medium hover:text-blue-900 transition-colors"
            onClick={(e) => {
              e.preventDefault();
              const url = planId
                ? `/audit/plans-daudit/${planId}/missions-audits/${missionAuditId}`
                : `/audit/missions-audits/${missionAuditId}`;
              navigate(url);
            }}
          >
            {missionAudit?.name || 'Mission d\'audit'}
          </BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbSeparator />
        <BreadcrumbItem>
          <BreadcrumbLink
            href={planId
              ? `/audit/plans-daudit/${planId}/missions-audits/${missionAuditId}/activites/${fiche?.auditActivityID}`
              : `/audit/missions-audits/${missionAuditId}/activites/${fiche?.auditActivityID}`
            }
            className="text-gray-500 font-medium hover:text-blue-900 transition-colors"
            onClick={(e) => {
              e.preventDefault();
              const url = planId
                ? `/audit/plans-daudit/${planId}/missions-audits/${missionAuditId}/activites/${fiche?.auditActivityID}`
                : `/audit/missions-audits/${missionAuditId}/activites/${fiche?.auditActivityID}`;
              navigate(url);
            }}
          >
            {activity?.name || `Activité ${fiche?.auditActivityID || ''}`}
          </BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbSeparator />
        <BreadcrumbItem>
          <BreadcrumbPage className="text-gray-800 font-medium">{fiche?.name || `Fiche ${ficheId}`}</BreadcrumbPage>
        </BreadcrumbItem>
      </BreadcrumbList>
    </Breadcrumb>
  );

  // Tab bar
  const TabBar = () => (
    <div className="bg-white rounded-lg shadow-sm mb-6">
      <div className="border-b border-gray-200">
        <nav className="flex overflow-x-auto">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => navigateToTab(tab.id)}
              className={`
                px-4 py-4 text-sm font-medium whitespace-nowrap border-b-2 flex items-center
                ${getCurrentTab() === tab.id
                  ? "border-[#F62D51] text-[#F62D51]"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}
              `}
            >
              {tab.icon}
              <span className="ml-2">{tab.label}</span>
            </button>
          ))}
        </nav>
      </div>
    </div>
  );

  const handleGoBack = () => {
    // Navigate back to the parent mission audit
    navigate(`/audit/plans-daudit/${planId}/missions-audits/${missionAuditId}`);
  };

  return (
    <div className="p-6 max-w-[1200px] mx-auto space-y-6">
      {/* Header with fiche information and breadcrumb */}
      <DetailHeader
        title={fiche.name}
        icon={<img src={ficheTravailIcon} alt="Fiche de travail" className="h-6 w-6" />}
        badges={[getStatusBadgeInfo()]}
        metadata={metadata}
        onBack={handleGoBack}
        backLabel="Retour à la liste des fiches de travail"
        breadcrumb={breadcrumb}
      />
      {/* Custom tab navigation */}
      <TabBar />
      {/* Tab content */}
      <OutletContext.Provider value={{ fiche, setFiche }}>
        <div className="bg-white rounded-lg shadow-sm p-6">
          {getCurrentTab() === "caracteristiques" && <CaracteristiquesTab />}
          {getCurrentTab() === "fiches-test" && <FichesTestTab />}
        </div>
      </OutletContext.Provider>
    </div>
  );
}

export default EditFichesTravail;
