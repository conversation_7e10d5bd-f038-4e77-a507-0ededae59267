const db = require('../../models');
const AuditMission = db.AuditMission;
const User = db.User;
const AuditPlan = db.AuditPlan;
const { v4: uuidv4 } = require('uuid');

// Get all audit missions
const getAllAuditMissions = async (req, res) => {
  try {
    // Optimized query with specific attributes and ordering
    const auditMissions = await AuditMission.findAll({
      attributes: [
        'id', 'name', 'categorie', 'code', 'etat', 'chefmission',
        'principalAudite', 'objectif', 'avancement', 'planifieInitialement',
        'evaluation', 'datedebut', 'datefin', 'pointfort', 'pointfaible',
        'auditplanID', 'createdAt', 'updatedAt'
      ],
      include: [
        {
          model: AuditPlan,
          as: 'auditPlan',
          attributes: ['id', 'name', 'status'],
          required: false // LEFT JOIN for better performance
        }
      ],
      order: [['createdAt', 'DESC']]
    });

    return res.status(200).json({
      success: true,
      data: auditMissions
    });
  } catch (error) {
    console.error('Error fetching audit missions:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch audit missions',
      error: error.message
    });
  }
};

// Get audit missions by plan ID
const getAuditMissionsByPlanId = async (req, res) => {
  try {
    const { planId } = req.params;

    // Optimized query with indexed where clause and minimal includes
    const auditMissions = await AuditMission.findAll({
      where: { auditplanID: planId },
      attributes: [
        'id', 'name', 'categorie', 'code', 'etat', 'chefmission',
        'principalAudite', 'objectif', 'avancement', 'planifieInitialement',
        'evaluation', 'datedebut', 'datefin', 'pointfort', 'pointfaible',
        'auditplanID', 'createdAt', 'updatedAt'
      ],
      order: [['createdAt', 'DESC']]
    });

    return res.status(200).json({
      success: true,
      data: auditMissions
    });
  } catch (error) {
    console.error('Error fetching audit missions by plan ID:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch audit missions',
      error: error.message
    });
  }
};

// Create a new audit mission
const createAuditMission = async (req, res) => {
  try {
    const {
      name,
      categorie,
      code,
      etat,
      chefmission,
      principalAudite,
      objectif,
      avancement,
      planifieInitialement,
      evaluation,
      datedebut,
      datefin,
      pointfort,
      pointfaible,
      auditplanID
    } = req.body;
    
    // Validate required fields
    if (!name) {
      return res.status(400).json({
        success: false,
        message: 'Name is required for audit mission'
      });
    }
    
    if (!auditplanID) {
      return res.status(400).json({
        success: false,
        message: 'Audit plan ID is required'
      });
    }
    
    // Check if audit plan exists
    const auditPlan = await AuditPlan.findByPk(auditplanID);
    if (!auditPlan) {
      return res.status(404).json({
        success: false,
        message: 'Audit plan not found'
      });
    }
    
    // Create the audit mission
    const auditMission = await AuditMission.create({
      id: `AM_${uuidv4().substring(0, 8)}`, // Generate a unique ID with prefix
      name,
      categorie,
      code,
      etat: etat || 'Planned',
      chefmission,
      principalAudite,
      objectif,
      avancement,
      planifieInitialement: planifieInitialement || false,
      evaluation,
      datedebut,
      datefin,
      pointfort,
      pointfaible,
      auditplanID
    });

    return res.status(201).json({
      success: true,
      message: 'Audit mission created successfully',
      data: auditMission
    });
  } catch (error) {
    console.error('Error creating audit mission:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to create audit mission',
      error: error.message
    });
  }
};

// Get audit mission by ID
const getAuditMissionById = async (req, res) => {
  try {
    const { id } = req.params;
    
    const auditMission = await AuditMission.findByPk(id, {
      include: [
        {
          model: AuditPlan,
          as: 'auditPlan',
          attributes: ['id', 'name']
        }
      ]
    });
    
    if (!auditMission) {
      return res.status(404).json({
        success: false,
        message: 'Audit mission not found'
      });
    }
    
    return res.status(200).json({
      success: true,
      data: auditMission
    });
  } catch (error) {
    console.error('Error fetching audit mission:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch audit mission',
      error: error.message
    });
  }
};

// Update audit mission
const updateAuditMission = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      name,
      categorie,
      code,
      etat,
      chefmission,
      principalAudite,
      objectif,
      avancement,
      planifieInitialement,
      evaluation,
      datedebut,
      datefin,
      pointfort,
      pointfaible,
      auditplanID
    } = req.body;
    
    const auditMission = await AuditMission.findByPk(id);
    
    if (!auditMission) {
      return res.status(404).json({
        success: false,
        message: 'Audit mission not found'
      });
    }
    
    // If auditplanID is being changed, check if the new plan exists
    if (auditplanID && auditplanID !== auditMission.auditplanID) {
      const auditPlan = await AuditPlan.findByPk(auditplanID);
      if (!auditPlan) {
        return res.status(404).json({
          success: false,
          message: 'New audit plan not found'
        });
      }
    }
    
    // Update the audit mission
    await auditMission.update({
      name: name || auditMission.name,
      categorie: categorie !== undefined ? categorie : auditMission.categorie,
      code: code !== undefined ? code : auditMission.code,
      etat: etat !== undefined ? etat : auditMission.etat,
      chefmission: chefmission !== undefined ? chefmission : auditMission.chefmission,
      principalAudite: principalAudite !== undefined ? principalAudite : auditMission.principalAudite,
      objectif: objectif !== undefined ? objectif : auditMission.objectif,
      avancement: avancement !== undefined ? avancement : auditMission.avancement,
      planifieInitialement: planifieInitialement !== undefined ? planifieInitialement : auditMission.planifieInitialement,
      evaluation: evaluation !== undefined ? evaluation : auditMission.evaluation,
      datedebut: datedebut !== undefined ? datedebut : auditMission.datedebut,
      datefin: datefin !== undefined ? datefin : auditMission.datefin,
      pointfort: pointfort !== undefined ? pointfort : auditMission.pointfort,
      pointfaible: pointfaible !== undefined ? pointfaible : auditMission.pointfaible,
      auditplanID: auditplanID || auditMission.auditplanID
    });
    
    // Fetch the updated audit mission with related data
    const updatedAuditMission = await AuditMission.findByPk(id, {
      include: [
        {
          model: AuditPlan,
          as: 'auditPlan',
          attributes: ['id', 'name']
        }
      ]
    });
    
    return res.status(200).json({
      success: true,
      message: 'Audit mission updated successfully',
      data: updatedAuditMission
    });
  } catch (error) {
    console.error('Error updating audit mission:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to update audit mission',
      error: error.message
    });
  }
};

// Delete audit mission
const deleteAuditMission = async (req, res) => {
  try {
    const { id } = req.params;
    
    const auditMission = await AuditMission.findByPk(id);
    
    if (!auditMission) {
      return res.status(404).json({
        success: false,
        message: 'Audit mission not found'
      });
    }
    
    await auditMission.destroy();
    
    return res.status(200).json({
      success: true,
      message: 'Audit mission deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting audit mission:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to delete audit mission',
      error: error.message
    });
  }
};

module.exports = {
  getAllAuditMissions,
  getAuditMissionsByPlanId,
  createAuditMission,
  getAuditMissionById,
  updateAuditMission,
  deleteAuditMission
};