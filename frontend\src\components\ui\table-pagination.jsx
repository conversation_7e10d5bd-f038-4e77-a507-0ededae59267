import { ChevronLeft, ChevronRight } from "lucide-react";
import { useEffect, useState } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "./select";

function TablePagination({
  currentPage = 1,
  totalPages,
  itemsPerPage = 10,
  totalItems,
  onPageChange,
  onItemsPerPageChange,
  startIndex,
  endIndex,
}) {
  console.log('TablePagination props:', { currentPage, itemsPerPage, totalPages, totalItems });
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);

  // Check sidebar collapsed state from localStorage and set up event listeners
  useEffect(() => {
    // Function to check and update sidebar state
    const checkSidebarState = () => {
      const savedState = localStorage.getItem('sidebarCollapsed');
      setIsSidebarCollapsed(savedState === 'true');
    };

    // Initial check
    checkSidebarState();

    // Listen for storage events to update in real-time across tabs
    const handleStorageChange = (e) => {
      if (e.key === 'sidebarCollapsed') {
        checkSidebarState();
      }
    };

    // Listen for custom events for changes within the same tab
    const handleCustomEvent = () => {
      checkSidebarState();
    };

    // Add event listeners
    window.addEventListener('storage', handleStorageChange);
    window.addEventListener('sidebarStateChanged', handleCustomEvent);

    // Clean up function
    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('sidebarStateChanged', handleCustomEvent);
    };
  }, []);

  return (
    <div
      className="fixed bottom-0 right-0 bg-white border-t shadow-lg"
      style={{
        left: isSidebarCollapsed ? '4rem' : '16rem', // 4rem = 64px, 16rem = 256px
        width: `calc(100% - ${isSidebarCollapsed ? '4rem' : '16rem'})`, // Dynamic calculation
        transition: 'left 0.3s ease, width 0.3s ease'
      }}
    >
      <div className="px-6 py-4 flex items-center justify-between w-full">
        <div className="text-sm text-gray-600">
          Showing {startIndex + 1} to {Math.min(endIndex, totalItems)} of {totalItems} items
        </div>

        <div className="flex items-center gap-4">
          {/* Rows per page selector */}
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600">Rows per page:</span>
            <Select
              value={(itemsPerPage || 10).toString()}
              onValueChange={(value) => {
                onItemsPerPageChange(Number(value));
              }}
            >
              <SelectTrigger className="w-[70px]">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {[5, 10, 20, 30, 50].map((number) => (
                  <SelectItem key={number} value={number.toString()}>
                    {number}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Page navigation */}
          <div className="flex items-center gap-2">
            <button
              onClick={() => onPageChange(currentPage - 1)}
              disabled={currentPage === 1}
              className={`p-2 rounded-lg ${
                currentPage === 1
                  ? "text-gray-400 cursor-not-allowed"
                  : "text-gray-600 hover:bg-gray-100"
              }`}
            >
              <ChevronLeft className="w-5 h-5" />
            </button>

            {/* Page selector */}
            <Select
              value={(currentPage || 1).toString()}
              onValueChange={(value) => onPageChange(Number(value))}
            >
              <SelectTrigger className="w-[70px]">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                  <SelectItem key={page} value={page.toString()}>
                    {page}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <span className="text-sm text-gray-600">of {totalPages}</span>

            <button
              onClick={() => onPageChange(currentPage + 1)}
              disabled={currentPage === totalPages}
              className={`p-2 rounded-lg ${
                currentPage === totalPages
                  ? "text-gray-400 cursor-not-allowed"
                  : "text-gray-600 hover:bg-gray-100"
              }`}
            >
              <ChevronRight className="w-5 h-5" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

export default TablePagination;