import axios from 'axios';
import { getApiBaseUrl } from '@/utils/api-config';

const API_BASE_URL = getApiBaseUrl();

/**
 * Fetch KPI counts for a specific audit plan
 * @param {string} planId - The audit plan ID
 * @param {AbortSignal} [signal] - Optional AbortSignal for request cancellation
 * @returns {Promise<{missionsCount: number, recommendationsCount: number}>}
 */
export const getAuditPlanKpiCounts = async (planId, signal) => {
  try {
    // Fetch missions and recommendations counts in parallel for optimization
    const [missionsResponse, recommendationsResponse] = await Promise.all([
      axios.get(`${API_BASE_URL}/audit-missions/plan/${planId}`, {
        withCredentials: true,
        headers: { 'Content-Type': 'application/json' },
        signal
      }),
      axios.get(`${API_BASE_URL}/audit-recommendations/audit-plan/${planId}`, {
        withCredentials: true,
        headers: { 'Content-Type': 'application/json' },
        signal
      })
    ]);

    const missionsCount = missionsResponse.data.success ? missionsResponse.data.data.length : 0;
    const recommendationsCount = recommendationsResponse.data.success ? recommendationsResponse.data.data.length : 0;

    return {
      missionsCount,
      recommendationsCount
    };

  } catch (error) {
    if (axios.isCancel(error)) {
      console.log('KPI counts request cancelled:', error.message);
      return null;
    }
    throw error;
  }
};

/**
 * Fetch missions count for a specific audit plan
 * @param {string} planId - The audit plan ID
 * @param {AbortSignal} [signal] - Optional AbortSignal for request cancellation
 * @returns {Promise<number>}
 */
export const getAuditPlanMissionsCount = async (planId, signal) => {
  try {
    const response = await axios.get(`${API_BASE_URL}/audit-missions/plan/${planId}`, {
      withCredentials: true,
      headers: { 'Content-Type': 'application/json' },
      signal
    });

    return response.data.success ? response.data.data.length : 0;
  } catch (error) {
    if (axios.isCancel(error)) {
      console.log('Missions count request cancelled:', error.message);
      return null;
    }
    throw error;
  }
};

/**
 * Fetch recommendations count for a specific audit plan
 * @param {string} planId - The audit plan ID
 * @param {AbortSignal} [signal] - Optional AbortSignal for request cancellation
 * @returns {Promise<number>}
 */
export const getAuditPlanRecommendationsCount = async (planId, signal) => {
  try {
    const response = await axios.get(`${API_BASE_URL}/audit-recommendations/audit-plan/${planId}`, {
      withCredentials: true,
      headers: { 'Content-Type': 'application/json' },
      signal
    });

    return response.data.success ? response.data.data.length : 0;
  } catch (error) {
    if (axios.isCancel(error)) {
      console.log('Recommendations count request cancelled:', error.message);
      return null;
    }
    throw error;
  }
};
