import { useOutletContext, usePara<PERSON>, useNavigate } from "react-router-dom";
import { useState, useEffect } from "react";
import { ListChecks, Search, Eye, Edit, Trash2, Plus, Loader2, AlertCircle } from "lucide-react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import TablePagination from "@/components/ui/table-pagination";
import { toast } from "sonner";
import axios from "axios";
import { getApiBaseUrl } from "@/utils/api-config";

function RecommandationsTab() {
  const { auditPlan } = useOutletContext();
  const { id: planId } = useParams();
  const navigate = useNavigate();

  const [recommendations, setRecommendations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Fetch recommendations for this specific plan
  useEffect(() => {
    const fetchRecommendations = async () => {
      if (!planId) return;

      try {
        setLoading(true);
        const response = await axios.get(`${getApiBaseUrl()}/audit-recommendations/audit-plan/${planId}`, {
          withCredentials: true,
          headers: {
            'Content-Type': 'application/json'
          }
        });

        if (response.data.success && response.data.data) {
          // Transform the data structure to flatten recommendations with their constats
          const transformedRecommendations = [];

          // The new endpoint returns an array of recommendations
          response.data.data.forEach(item => {
            const recommendation = item.recommendation;
            const constats = item.constats;

            // Create an entry for each constat linked to this recommendation
            constats.forEach(constat => {
              transformedRecommendations.push({
                id: recommendation.id,
                name: recommendation.name,
                priorite: recommendation.priorite,
                description: recommendation.description,
                planification: recommendation.planification,
                code: recommendation.code,
                details: recommendation.details,
                constatId: constat.id,
                constatName: constat.name,
                activityId: constat.activity?.id,
                activityName: constat.activity?.name,
                missionId: constat.activity?.mission?.id,
                missionName: constat.activity?.mission?.name,
                planId: constat.activity?.mission?.plan?.id,
                planName: constat.activity?.mission?.plan?.name
              });
            });
          });

          setRecommendations(transformedRecommendations);
        } else {
          setRecommendations([]);
        }
      } catch (error) {
        console.error("Error fetching recommendations:", error);
        if (error.response?.status === 404) {
          // No recommendations found for this plan
          setRecommendations([]);
        } else {
          toast.error("Erreur lors du chargement des recommandations");
        }
      } finally {
        setLoading(false);
      }
    };

    fetchRecommendations();
  }, [planId]);

  // Filter recommendations based on search query
  const filteredRecommendations = recommendations.filter(rec =>
    rec.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    rec.constatName?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    rec.activityName?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    rec.missionName?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Pagination
  const totalPages = Math.ceil(filteredRecommendations.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = Math.min(startIndex + itemsPerPage, filteredRecommendations.length);
  const currentRecommendations = filteredRecommendations.slice(startIndex, endIndex);

  const getModuleBadge = (module) => {
    const moduleConfig = {
      'Mission': { color: 'bg-blue-100 text-blue-800', label: 'Mission' },
      'Activité': { color: 'bg-green-100 text-green-800', label: 'Activité' },
      'Constat': { color: 'bg-purple-100 text-purple-800', label: 'Constat' }
    };

    const config = moduleConfig[module] || { color: 'bg-gray-100 text-gray-800', label: module || 'Non défini' };
    return <Badge className={config.color}>{config.label}</Badge>;
  };

  const handleRowClick = (recommendationId) => {
    navigate(`/audit/plans-daudit/${planId}/recommandations/${recommendationId}`);
  };

  if (!auditPlan) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#F62D51] mx-auto mb-4"></div>
          <p className="text-gray-500">Chargement des informations du plan d'audit...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <ListChecks className="h-6 w-6 text-[#F62D51]" />
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Recommandations</h2>
            <p className="text-sm text-gray-600">
              Plan d'audit: <span className="font-medium">{auditPlan.name}</span>
            </p>
          </div>
        </div>
        <Button className="bg-[#F62D51] hover:bg-[#E02347]">
          <Plus className="h-4 w-4 mr-2" />
          Nouvelle recommandation
        </Button>
      </div>

      {/* Search */}
      <div className="flex items-center gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Rechercher par recommandation, constat, activité ou mission..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Liste des recommandations ({filteredRecommendations.length})</span>
            {loading && <Loader2 className="h-4 w-4 animate-spin" />}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#F62D51] mx-auto mb-4"></div>
                <p className="text-gray-500">Chargement des recommandations...</p>
              </div>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Recommandation</TableHead>
                    <TableHead>Constat</TableHead>
                    <TableHead>Activité</TableHead>
                    <TableHead>Mission</TableHead>
                    <TableHead>Plan d'audit</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {currentRecommendations.length > 0 ? (
                    currentRecommendations.map((recommendation, index) => (
                      <TableRow 
                        key={`${recommendation.id}-${recommendation.constatId}-${index}`}
                        onClick={() => handleRowClick(recommendation.id)}
                        className="cursor-pointer"
                      >
                        <TableCell className="font-medium">
                          <div className="flex flex-col">
                            <span className="font-semibold text-gray-900">
                              {recommendation.name || 'Sans nom'}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex flex-col">
                            <span className="font-medium text-gray-800">
                              {recommendation.constatName || '-'}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex flex-col">
                            <span className="font-medium text-gray-800">
                              {recommendation.activityName || '-'}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex flex-col">
                            <span className="font-medium text-gray-800">
                              {recommendation.missionName || '-'}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex flex-col">
                            <span className="font-medium text-gray-800">
                              {recommendation.planName || auditPlan.name}
                            </span>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={5} className="h-24 text-center text-gray-500">
                        Aucune recommandation trouvée.
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      <TablePagination
        totalPages={totalPages}
        currentPage={currentPage}
        onPageChange={setCurrentPage}
        hasItems={filteredRecommendations.length > 0}
      />
    </div>
  );
}

export default RecommandationsTab;
