import React, { useState, useEffect } from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Plus, Search, Edit, ExternalLink, Loader2 } from "lucide-react";
import { toast } from "sonner";
import { useNavigate, useParams } from "react-router-dom";
import axios from 'axios';
import { getApiBaseUrl } from '@/utils/api-config';

// Import activité icon
import activiteIcon from '@/assets/activite.png';

function ActivitesTab() {
  const navigate = useNavigate();
  const { planId, missionAuditId } = useParams();

  const [activities, setActivities] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");

  // Fetch activities for the current mission
  useEffect(() => {
    const fetchActivities = async () => {
      if (!missionAuditId) return;

      try {
        setIsLoading(true);
        const response = await axios.get(`${getApiBaseUrl()}/audit-activities/mission/${missionAuditId}`, {
          withCredentials: true,
          headers: { 'Content-Type': 'application/json' }
        });

        if (response.data.success) {
          setActivities(response.data.data || []);
        } else {
          console.error('Failed to fetch activities:', response.data.message);
          setActivities([]);
        }
      } catch (error) {
        console.error('Error fetching activities:', error);
        toast.error('Erreur lors du chargement des activités');
        setActivities([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchActivities();
  }, [missionAuditId]);

  // Filter activities based on search term
  const filteredActivities = activities.filter(activity =>
    activity.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    activity.nom?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Navigate to activity detail page
  const navigateToActivity = (activityId) => {
    if (planId && missionAuditId) {
      navigate(`/audit/plans-daudit/${planId}/missions-audits/${missionAuditId}/activites/${activityId}`);
    }
  };

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return "Non définie";
    try {
      return new Date(dateString).toLocaleDateString("fr-FR", {
        year: "numeric",
        month: "short",
        day: "numeric",
      });
    } catch {
      return dateString;
    }
  };

  // Get status badge for activity
  const getStatusBadge = (status) => {
    switch (status) {
      case 'Terminé':
      case 'Completed':
        return <Badge className="bg-green-100 text-green-800">Terminé</Badge>;
      case 'En cours':
      case 'In Progress':
        return <Badge className="bg-blue-100 text-blue-800">En cours</Badge>;
      case 'Planifié':
      case 'Planned':
        return <Badge className="bg-yellow-100 text-yellow-800">Planifié</Badge>;
      case 'Suspendu':
      case 'Suspended':
        return <Badge className="bg-orange-100 text-orange-800">Suspendu</Badge>;
      case 'Annulé':
      case 'Cancelled':
        return <Badge className="bg-red-100 text-red-800">Annulé</Badge>;
      default:
        return <Badge variant="outline">{status || 'Non défini'}</Badge>;
    }
  };

  return (
    <div className="space-y-6 py-4">
      {/* Header with search and add button */}
      <div className="mb-4 flex justify-between items-center">
        <div className="relative w-64">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Rechercher dans les activités..."
            className="pl-8"
            value={searchTerm}
            onChange={e => setSearchTerm(e.target.value)}
          />
        </div>
        <Button
          className="bg-[#F62D51] hover:bg-[#F62D51]/90"
          onClick={() => {
            // Navigate to create new activity page
            if (planId && missionAuditId) {
              navigate(`/audit/plans-daudit/${planId}/missions-audits/${missionAuditId}/activites/new`);
            }
          }}
        >
          <Plus className="h-4 w-4 mr-2" />
          Ajouter une Activité
        </Button>
      </div>

      {/* Activities Table */}
      <Card className="overflow-hidden">
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <Table className="min-w-full">
              <TableHeader>
                <TableRow className="bg-gray-50 hover:bg-gray-100/50">
                  <TableHead className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nom</TableHead>
                  <TableHead className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Statut</TableHead>
                  <TableHead className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date de début</TableHead>
                  <TableHead className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date de fin</TableHead>
                  <TableHead className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Constats</TableHead>
                  <TableHead className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody className="divide-y divide-gray-200">
                {isLoading ? (
                  <TableRow>
                    <TableCell colSpan={6} className="px-4 py-10 text-center">
                      <div className="flex items-center justify-center">
                        <Loader2 className="h-8 w-8 animate-spin text-[#F62D51]" />
                      </div>
                    </TableCell>
                  </TableRow>
                ) : filteredActivities.length > 0 ? (
                  filteredActivities.map(activity => (
                    <TableRow
                      key={activity.id}
                      className="hover:bg-gray-50/50 cursor-pointer"
                      onClick={() => navigateToActivity(activity.id)}
                    >
                      <TableCell className="px-4 py-3 text-sm font-medium text-gray-900 whitespace-nowrap">
                        <div className="flex items-center">
                          <img src={activiteIcon} alt="Activité" className="h-4 w-4 mr-2 flex-shrink-0" />
                          <span>{activity.name || activity.nom}</span>
                          <ExternalLink className="h-3.5 w-3.5 ml-2 text-gray-400" />
                        </div>
                      </TableCell>
                      <TableCell className="px-4 py-3 text-sm whitespace-nowrap">
                        {getStatusBadge(activity.status || activity.statut)}
                      </TableCell>
                      <TableCell className="px-4 py-3 text-sm text-gray-500 whitespace-nowrap">
                        {formatDate(activity.startDate || activity.dateDebut)}
                      </TableCell>
                      <TableCell className="px-4 py-3 text-sm text-gray-500 whitespace-nowrap">
                        {formatDate(activity.endDate || activity.dateFin)}
                      </TableCell>
                      <TableCell className="px-4 py-3 text-sm text-gray-500 whitespace-nowrap">
                        {activity.constatsCount || 0}
                      </TableCell>
                      <TableCell className="px-4 py-3 text-sm whitespace-nowrap">
                        <div className="flex justify-end gap-2">
                          <Button
                            size="sm"
                            variant="ghost"
                            className="h-8 w-8 p-0"
                            onClick={e => {
                              e.stopPropagation();
                              navigateToActivity(activity.id);
                            }}
                          >
                            <Edit className="h-4 w-4 text-blue-600" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={6} className="px-4 py-10 text-center text-sm text-gray-500">
                      {searchTerm ? 'Aucune activité trouvée pour cette recherche' : 'Aucune activité dans cette mission'}
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );

}

export default ActivitesTab; 